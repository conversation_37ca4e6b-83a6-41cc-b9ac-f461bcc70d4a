import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog, filedialog
from datetime import datetime, date, timedelta
import calendar
import json
import os
import tempfile
import subprocess
import webbrowser
from tkinter import font as tkfont
import sys
import traceback

# بديل للرسوم البيانية إذا لم يكن matplotlib مثبتاً أو متوافقاً
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

# بديل للتقويم
class SimpleCalendar:
    def __init__(self, master, **kwargs):
        self.master = master
        self.date = date.today()
        
        self.frame = ttk.Frame(master)
        self.frame.pack(padx=10, pady=10)
        
        # عناصر بسيطة لإدخال التاريخ
        ttk.Label(self.frame, text="اليوم:").grid(row=0, column=0)
        self.day_var = tk.StringVar(value=str(self.date.day))
        ttk.Spinbox(self.frame, from_=1, to=31, textvariable=self.day_var, width=5).grid(row=0, column=1)
        
        ttk.Label(self.frame, text="الشهر:").grid(row=0, column=2)
        self.month_var = tk.StringVar(value=str(self.date.month))
        ttk.Spinbox(self.frame, from_=1, to=12, textvariable=self.month_var, width=5).grid(row=0, column=3)
        
        ttk.Label(self.frame, text="السنة:").grid(row=0, column=4)
        self.year_var = tk.StringVar(value=str(self.date.year))
        ttk.Spinbox(self.frame, from_=2020, to=2030, textvariable=self.year_var, width=7).grid(row=0, column=5)
    
    def selection_get(self):
        try:
            return date(int(self.year_var.get()), 
                       int(self.month_var.get()), 
                       int(self.day_var.get()))
        except:
            return date.today()

class AccountingApp:
    def __init__(self, root):
        try:
            self.root = root
            self.root.title("برنامج المحاسبة المتكامل - الإصدار المحسن")
            self.root.geometry("1300x850")
            self.root.configure(bg='#f5f5f5')
            
            # منع الإغلاق المفاجئ
            self.root.protocol("WM_DELETE_WINDOW", self.confirm_exit)
            
            # تحسين الخطوط للواجهة العربية
            self.default_font = tkfont.nametofont("TkDefaultFont")
            self.default_font.configure(family="Arial", size=10)
            self.text_font = tkfont.nametofont("TkTextFont")
            self.text_font.configure(family="Arial", size=10)
            
            # ألوان زاهية ومتناسقة
            self.colors = {
                'bg': '#f5f5f5',
                'button': '#4CAF50',
                'button_hover': '#45a049',
                'button_text': 'white',
                'header': '#2196F3',
                'tab_bg': '#e3f2fd',
                'tab_active': '#bbdefb',
                'accent': '#FF9800',
                'warning': '#f44336'
            }
            
            # بيانات التطبيق مع الترتيب الجديد
            self.data = {
                "الإنترنت": {},
                "الشبكة": {},
                "البلاستيشن": {},
                "استديو كانون": {},
                "استديو غزة": {}
            }
            
            # بيانات اليوم الحالي (تراكمية)
            self.today_data = {}
            
            # إعدادات التطبيق
            self.settings = {
                "auto_save": True,
                "backup_on_exit": True,
                "printer": "الطابعة الافتراضية",
                "currency": "شيكل"  # العملة الافتراضية
            }
            
            # تحميل البيانات والإعدادات
            self.load_data()
            self.load_settings()
            
            # إنشاء واجهة المستخدم
            self.create_menu()
            self.create_toolbar()
            self.create_widgets()
            
            # تحديث حالة الحفظ التلقائي
            self.update_auto_save_status()
            
            # جدولة تجميع البيانات في نهاية اليوم
            self.schedule_end_of_day_processing()
            
        except Exception as e:
            messagebox.showerror("خطأ فادح", f"حدث خطأ أثناء تهيئة التطبيق: {str(e)}\n\n{''.join(traceback.format_exception(*sys.exc_info()))}")
            self.root.quit()
    
    def schedule_end_of_day_processing(self):
        # حساب الوقت المتبقي حتى منتصف الليل
        now = datetime.now()
        midnight = now.replace(hour=23, minute=59, second=59, microsecond=999999)
        time_until_midnight = (midnight - now).total_seconds() * 1000  # بالميلي ثانية
        
        # جدولة تجميع البيانات قبل منتصف الليل بدقيقة واحدة
        self.root.after(int(time_until_midnight), self.process_end_of_day)
    
    def process_end_of_day(self):
        try:
            # تجميع جميع بيانات اليوم لكل قسم
            for section in self.data.keys():
                if section in self.today_data and self.today_data[section]:
                    # إنشاء تاريخ اليوم كسلسلة
                    today_str = date.today().strftime("%Y-%m-%d")
                    
                    # تجميع جميع القيم لهذا اليوم
                    total_income = sum(float(data.get("الدخل", 0) or 0) for data in self.today_data[section].values())
                    total_expenses = sum(float(data.get("الصرفة", 0) or 0) for data in self.today_data[section].values())
                    total_electricity = sum(float(data.get("الكهرباء", 0) or 0) for data in self.today_data[section].values())
                    total_internet = sum(float(data.get("تسديد انترنت", 0) or 0) for data in self.today_data[section].values())
                    total_rent = sum(float(data.get("الايجار", 0) or 0) for data in self.today_data[section].values())
                    
                    # جمع جميع الملاحظات
                    all_notes = " | ".join([data.get("ملاحظات", "") for data in self.today_data[section].values() if data.get("ملاحظات")])
                    
                    # حساب الربح/الخسارة
                    profit = total_income - (total_expenses + total_electricity + total_internet + total_rent)
                    
                    # حفظ البيانات المجمعة
                    if today_str not in self.data[section]:
                        self.data[section][today_str] = {}
                    
                    self.data[section][today_str] = {
                        "الدخل": total_income,
                        "الصرفة": total_expenses,
                        "الكهرباء": total_electricity,
                        "تسديد انترنت": total_internet,
                        "الايجار": total_rent,
                        "الربح": profit,
                        "ملاحظات": all_notes
                    }
                    
                    # تحديث الشجرة
                    self.update_tree(section)
            
            # حفظ البيانات
            self.save_to_file()
            
            # مسح بيانات اليوم
            self.today_data = {}
            
            # إعادة جدولة المعالجة لليوم التالي
            self.schedule_end_of_day_processing()
        except Exception as e:
            print(f"خطأ في معالجة نهاية اليوم: {str(e)}")
    
    def create_menu(self):
        # إنشاء شريط القوائم مع المحاذاة لليمين
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة المساعدة (تظهر على أقصى اليمين)
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="عن البرنامج", command=self.about)

        # قائمة الإعدادات (تظهر بعد المساعدة)
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="إعدادات", menu=settings_menu)
        settings_menu.add_command(label="إعدادات التطبيق", command=self.open_settings)

        # قائمة التقارير (تظهر بعد الإعدادات)
        report_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="تقارير", menu=report_menu)
        report_menu.add_command(label="تقرير شامل", command=self.show_comprehensive_report)
        report_menu.add_command(label="تقرير شهري", command=self.generate_monthly_report)
        report_menu.add_command(label="تقرير سنوي", command=self.generate_yearly_report)

        # قائمة الملف (تظهر على أقصى اليسار)
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_to_file)
        file_menu.add_command(label="حفظ باسم", command=self.save_as_file)
        file_menu.add_separator()
        file_menu.add_command(label="إدارة الأقسام", command=self.manage_sections)

        # إضافة متغير للحفظ التلقائي
        self.auto_save_var = tk.BooleanVar(value=self.settings["auto_save"])
        file_menu.add_checkbutton(label="الحفظ التلقائي", variable=self.auto_save_var,
                                 command=self.toggle_auto_save)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.confirm_exit)
        
    def create_toolbar(self):
        # إنشاء شريط أدوات مع المحاذاة لليمين
        toolbar = tk.Frame(self.root, bd=1, relief=tk.RAISED, bg=self.colors['bg'])
        toolbar.pack(side=tk.TOP, fill=tk.X)
        
        # أزرار شريط الأدوات (تبدأ من اليمين)
        buttons = [
            ("فتح", self.open_file, "📂"),
            ("إضافة", self.add_new_record, "➕"),
            ("حفظ", self.save_to_file, "💾"),
            ("طباعة", self.print_report_dialog, "🖨️"),
            ("تقرير", self.show_comprehensive_report, "📊"),
            ("إعدادات", self.open_settings, "⚙️")
        ]
        
        for text, command, icon in buttons:  # الترتيب من اليمين لليسار
            btn = tk.Button(toolbar, text=f"{icon} {text}", command=command, 
                           bg=self.colors['button'], fg=self.colors['button_text'],
                           relief=tk.FLAT, padx=5, pady=2)
            btn.pack(side=tk.RIGHT, padx=2, pady=2)  # التعبئة من اليمين
            # إضافة تأثير عند المرور بالفأرة
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg=self.colors['button_hover']))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(bg=self.colors['button']))
        
        # عرض حالة الحفظ (على اليسار)
        self.save_status = tk.Label(toolbar, text="", bg=self.colors['bg'], fg="green")
        self.save_status.pack(side=tk.LEFT, padx=5)
        
    def update_auto_save_status(self):
        if self.settings["auto_save"]:
            self.save_status.config(text="الحفظ التلقائي مفعل")
        else:
            self.save_status.config(text="الحفظ التلقائي معطل")
            
    def toggle_auto_save(self):
        self.settings["auto_save"] = not self.settings["auto_save"]
        self.update_auto_save_status()
        self.save_settings()
        
    def open_settings(self):
        # تحديد تبويب الإعدادات
        self.notebook.select(len(self.data) + 1)  # الانتقال إلى تبويب الإعدادات
    
    def add_new_record(self):
        # فتح نافذة لإضافة سجل جديد
        try:
            current_tab = self.notebook.index(self.notebook.select())
            if current_tab < len(self.data):
                section = list(self.data.keys())[current_tab]
                self.clear_entries(section)
            else:
                messagebox.showinfo("معلومة", "الرجاء اختيار قسم لإضافة سجل جديد")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة سجل جديد: {str(e)}")
    
    def manage_sections(self):
        # نافذة إدارة الأقسام
        try:
            manage_window = tk.Toplevel(self.root)
            manage_window.title("إدارة الأقسام")
            manage_window.geometry("500x400")
            manage_window.configure(bg=self.colors['bg'])
            manage_window.transient(self.root)
            manage_window.grab_set()
            
            # جعل النافذة في المركز
            manage_window.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() - manage_window.winfo_width()) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - manage_window.winfo_height()) // 2
            manage_window.geometry(f"+{x}+{y}")
            
            header = tk.Label(manage_window, text="إدارة الأقسام", font=('Arial', 16, 'bold'), 
                             bg=self.colors['header'], fg='white', pady=10)
            header.pack(fill='x', pady=(0, 10))
            
            # إطار محتوى
            content_frame = tk.Frame(manage_window, bg=self.colors['bg'])
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # قائمة الأقسام الحالية
            list_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            list_frame.pack(fill='both', expand=True, pady=5)
            
            tk.Label(list_frame, text="الأقسام الحالية:", bg=self.colors['bg']).pack(anchor='w')
            
            listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE, height=10, font=('Arial', 10))
            listbox.pack(fill='both', expand=True, pady=5)
            
            for section in self.data.keys():
                listbox.insert(tk.END, section)
            
            # أزرار الإدارة (محاذاة لليمين)
            button_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            button_frame.pack(fill='x', pady=10)
            
            add_btn = tk.Button(button_frame, text="إضافة قسم", command=lambda: self.add_section(listbox),
                               bg=self.colors['button'], fg=self.colors['button_text'], width=12)
            add_btn.pack(side=tk.RIGHT, padx=5)  # التعبئة من اليمين
            
            edit_btn = tk.Button(button_frame, text="تعديل اسم", command=lambda: self.edit_section(listbox),
                                bg=self.colors['accent'], fg='white', width=12)
            edit_btn.pack(side=tk.RIGHT, padx=5)
            
            remove_btn = tk.Button(button_frame, text="حذف قسم", command=lambda: self.remove_section(listbox),
                                  bg=self.colors['warning'], fg='white', width=12)
            remove_btn.pack(side=tk.RIGHT, padx=5)
            
            close_btn = tk.Button(button_frame, text="إغلاق", command=manage_window.destroy,
                                 bg=self.colors['button'], fg=self.colors['button_text'], width=12)
            close_btn.pack(side=tk.RIGHT, padx=5)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح نافذة إدارة الأقسام: {str(e)}")
    
    def add_section(self, listbox):
        try:
            section_name = simpledialog.askstring("إضافة قسم", "أدخل اسم القسم الجديد:")
            if section_name and section_name not in self.data:
                self.data[section_name] = {}
                listbox.insert(tk.END, section_name)
                self.update_notebook()
                self.save_to_file()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة القسم: {str(e)}")
    
    def edit_section(self, listbox):
        try:
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار قسم لتعديله")
                return
                
            old_name = listbox.get(selection[0])
            new_name = simpledialog.askstring("تعديل قسم", "أدخل الاسم الجديد للقسم:", initialvalue=old_name)
            
            if new_name and new_name != old_name:
                if new_name in self.data:
                    messagebox.showerror("خطأ", "اسم القسم موجود مسبقاً")
                    return
                    
                # نقل البيانات إلى الاسم الجديد
                self.data[new_name] = self.data.pop(old_name)
                listbox.delete(selection[0])
                listbox.insert(selection[0], new_name)
                self.update_notebook()
                self.save_to_file()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل القسم: {str(e)}")
    
    def remove_section(self, listbox):
        try:
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار قسم لحذفه")
                return
                
            section_name = listbox.get(selection[0])
            if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف قسم '{section_name}'؟\nسيتم حذف جميع البيانات المرتبطة بهذا القسم!"):
                # تأكيد إضافي إذا كان القسم يحتوي على بيانات
                if self.data[section_name]:
                    if not messagebox.askyesno("تأكيد نهائي", "هذا القسم يحتوي على بيانات. هل أنت متأكد من الحذف؟"):
                        return
                
                self.data.pop(section_name)
                listbox.delete(selection[0])
                self.update_notebook()
                self.save_to_file()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف القسم: {str(e)}")
    
    def update_notebook(self):
        # إعادة إنشاء دفتر الملاحظات مع الأقسام المحدثة
        try:
            for child in self.notebook.winfo_children():
                child.destroy()
            
            self.frames = {}
            # الترتيب المطلوب للأقسام (من اليمين لليسار)
            ordered_sections = ["الإنترنت", "الشبكة", "البلاستيشن", "استديو كانون", "استديو غزة"]

            # إضافة الأقسام بالترتيب المحدد
            for section in ordered_sections:
                if section in self.data:
                    self.frames[section] = ttk.Frame(self.notebook, padding="10")
                    self.notebook.add(self.frames[section], text=section)
                    self.create_section_ui(section)
            
            # إضافة أي أقسام أخرى ليست في القائمة المحددة
            for section in self.data.keys():
                if section not in ordered_sections:
                    self.frames[section] = ttk.Frame(self.notebook, padding="10")
                    self.notebook.add(self.frames[section], text=section)
                    self.create_section_ui(section)
            
            # إعادة إنشاء إطار التقرير الشامل
            self.report_frame = ttk.Frame(self.notebook, padding="10")
            self.notebook.add(self.report_frame, text="التقرير الشامل")
            self.create_report_ui()
            
            # إعادة إنشاء إطار الإعدادات
            self.settings_frame = ttk.Frame(self.notebook, padding="10")
            self.notebook.add(self.settings_frame, text="الإعدادات")
            self.create_settings_ui()
            
            # تحديد أول تبويب
            self.notebook.select(0)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث دفتر الملاحظات: {str(e)}")
    
    def create_widgets(self):
        # إنشاء تبويبات للأقسام مع المحاذاة لليمين
        try:
            style = ttk.Style()
            style.configure("TNotebook", background=self.colors['tab_bg'], tabposition='ne')
            style.configure("TNotebook.Tab", background=self.colors['tab_bg'], padding=[10, 5])
            style.map("TNotebook.Tab", background=[("selected", self.colors['tab_active'])])

            # إنشاء إطار للتبويبات مع محاذاة يمين
            notebook_frame = tk.Frame(self.root, bg=self.colors['bg'])
            notebook_frame.pack(fill='both', expand=True, padx=10, pady=10)

            self.notebook = ttk.Notebook(notebook_frame)
            self.notebook.pack(fill='both', expand=True, anchor='e')
            
            # إنشاء إطارات لكل قسم بالترتيب المطلوب (من اليمين لليسار)
            self.frames = {}
            ordered_sections = ["الإنترنت", "الشبكة", "البلاستيشن", "استديو كانون", "استديو غزة"]

            for section in ordered_sections:
                if section in self.data:
                    self.frames[section] = ttk.Frame(self.notebook, padding="10")
                    self.notebook.add(self.frames[section], text=section)
                    self.create_section_ui(section)

            # إضافة أي أقسام أخرى ليست في القائمة المحددة
            for section in self.data.keys():
                if section not in ordered_sections:
                    self.frames[section] = ttk.Frame(self.notebook, padding="10")
                    self.notebook.add(self.frames[section], text=section)
                    self.create_section_ui(section)

            # إطار للتقرير الشامل
            self.report_frame = ttk.Frame(self.notebook, padding="10")
            self.notebook.add(self.report_frame, text="التقرير الشامل")
            self.create_report_ui()

            # إطار للإعدادات
            self.settings_frame = ttk.Frame(self.notebook, padding="10")
            self.notebook.add(self.settings_frame, text="الإعدادات")
            self.create_settings_ui()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الواجهة: {str(e)}")
            
    def create_section_ui(self, section):
        try:
            frame = self.frames[section]
            frame.configure(style='TFrame')
            
            # عنوان القسم
            title_frame = tk.Frame(frame, bg=self.colors['header'])
            title_frame.pack(fill='x', pady=(0, 10))
            
            title_label = tk.Label(title_frame, text=section, font=('Arial', 16, 'bold'), 
                                  bg=self.colors['header'], fg='white', pady=10)
            title_label.pack()
            
            # إطار رئيسي للمحتوى
            main_frame = tk.Frame(frame, bg=self.colors['bg'])
            main_frame.pack(fill='both', expand=True)
            
            # إطار لإدخال البيانات مع محاذاة يمين
            input_frame = tk.LabelFrame(main_frame, text="إدخال البيانات", font=('Arial', 12, 'bold'),
                                       bg=self.colors['bg'], padx=10, pady=10)
            input_frame.pack(fill='x', pady=(0, 10), anchor='e')

            # التاريخ التلقائي مع محاذاة يمين
            today = date.today()
            date_frame = tk.Frame(input_frame, bg=self.colors['bg'])
            date_frame.pack(fill='x', pady=5, anchor='e')

            # زر اختيار التاريخ (أقصى اليمين)
            date_btn = tk.Button(date_frame, text="📅 اختر تاريخ", command=lambda: self.choose_date(section),
                                bg=self.colors['button'], fg=self.colors['button_text'],
                                font=('Arial', 10), relief=tk.FLAT)
            date_btn.pack(side=tk.RIGHT, padx=5)

            if not hasattr(self, 'date_vars'):
                self.date_vars = {}
            self.date_vars[section] = tk.StringVar(value=today.strftime("%Y-%m-%d"))
            date_entry = tk.Entry(date_frame, textvariable=self.date_vars[section], state='readonly',
                                 width=15, font=('Arial', 10), justify='center')
            date_entry.pack(side=tk.RIGHT, padx=5)

            date_label = tk.Label(date_frame, text="التاريخ:", bg=self.colors['bg'], font=('Arial', 10))
            date_label.pack(side=tk.RIGHT, padx=5)
            
            # الحقول الأخرى
            fields = ["الدخل", "الصرفة", "الكهرباء", "تسديد انترنت", "الايجار", "ملاحظات"]
            if not hasattr(self, 'entry_vars'):
                self.entry_vars = {}
            self.entry_vars[section] = {}
            
            for i, field in enumerate(fields):
                field_frame = tk.Frame(input_frame, bg=self.colors['bg'])
                field_frame.pack(fill='x', pady=5, anchor='e')

                if field == "ملاحظات":
                    var = tk.StringVar()
                    entry = tk.Entry(field_frame, textvariable=var, font=('Arial', 10))
                    self.entry_vars[section][field] = var
                    entry.pack(side=tk.RIGHT, fill='x', expand=True, padx=5)
                else:
                    # إضافة علامة العملة للحقول الرقمية (أقصى اليمين)
                    currency_label = tk.Label(field_frame, text=self.get_currency_symbol(), bg=self.colors['bg'], font=('Arial', 10))
                    currency_label.pack(side=tk.RIGHT, padx=5)

                    var = tk.DoubleVar(value=0.0)
                    entry = tk.Entry(field_frame, textvariable=var, font=('Arial', 10), justify='right', width=15)
                    self.entry_vars[section][field] = var
                    entry.pack(side=tk.RIGHT, padx=5)

                label = tk.Label(field_frame, text=f"{field}:", bg=self.colors['bg'], font=('Arial', 10),
                               width=15, anchor='e')
                label.pack(side=tk.RIGHT, padx=5)
            
            # أزرار الإجراءات (محاذاة لليمين)
            button_frame = tk.Frame(input_frame, bg=self.colors['bg'])
            button_frame.pack(fill='x', pady=10, anchor='e')

            cancel_btn = tk.Button(button_frame, text="❌ إلغاء", command=lambda: self.clear_entries(section),
                                  bg=self.colors['warning'], fg='white',
                                  font=('Arial', 10), width=10)
            cancel_btn.pack(side=tk.RIGHT, padx=5)

            save_btn = tk.Button(button_frame, text="💾 حفظ", command=lambda: self.save_data(section),
                                bg=self.colors['button'], fg=self.colors['button_text'],
                                font=('Arial', 10, 'bold'), width=10)
            save_btn.pack(side=tk.RIGHT, padx=5)  # التعبئة من اليمين
            
            # جدول عرض البيانات مع محاذاة يمين
            table_frame = tk.LabelFrame(main_frame, text="السجلات", font=('Arial', 12, 'bold'),
                                       bg=self.colors['bg'], padx=10, pady=10)
            table_frame.pack(fill='both', expand=True, pady=(10, 0), anchor='e')

            columns = ("التاريخ", "الدخل", "الصرفة", "الكهرباء", "تسديد انترنت", "الايجار", "الربح/الخسارة", "ملاحظات")
            if not hasattr(self, 'trees'):
                self.trees = {}

            # إطار للشريط والجدول مع محاذاة يمين
            tree_container = tk.Frame(table_frame, bg=self.colors['bg'])
            tree_container.pack(fill='both', expand=True, anchor='e')

            tree = ttk.Treeview(tree_container, columns=columns, show='headings', height=10)

            # تكوين الأعمدة مع محاذاة يمين للنصوص العربية
            column_widths = [100, 80, 80, 80, 100, 80, 120, 150]
            for col, width in zip(columns, column_widths):
                tree.heading(col, text=col, anchor='e')  # محاذاة العناوين لليمين
                tree.column(col, width=width, anchor='e')  # محاذاة المحتوى لليمين

            # إضافة شريط التمرير
            scrollbar = ttk.Scrollbar(tree_container, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            scrollbar.pack(side=tk.RIGHT, fill='y')  # شريط التمرير على اليمين
            tree.pack(side=tk.RIGHT, fill='both', expand=True)  # الجدول على اليمين
            
            # إضافة قائمة السياق (النقر بزر الماوس الأيمن) للتعديل
            tree.bind("<Button-3>", lambda event: self.show_context_menu(event, section))
            
            self.trees[section] = tree
            
            # أزرار التقارير والإجراءات (محاذاة لليمين)
            action_frame = tk.Frame(table_frame, bg=self.colors['bg'])
            action_frame.pack(fill='x', pady=10, anchor='e')

            export_btn = tk.Button(action_frame, text="📤 تصدير",
                                  command=lambda: self.export_section_data(section),
                                  bg=self.colors['button'], fg=self.colors['button_text'],
                                  font=('Arial', 10))
            export_btn.pack(side=tk.RIGHT, padx=5)

            print_btn = tk.Button(action_frame, text="🖨️ طباعة",
                                 command=lambda: self.print_report_dialog(section),
                                 bg=self.colors['accent'], fg='white',
                                 font=('Arial', 10))
            print_btn.pack(side=tk.RIGHT, padx=5)

            monthly_btn = tk.Button(action_frame, text="📆 تقرير شهري",
                                   command=lambda: self.generate_report(section, "monthly"),
                                   bg=self.colors['button'], fg=self.colors['button_text'],
                                   font=('Arial', 10))
            monthly_btn.pack(side=tk.RIGHT, padx=5)

            daily_btn = tk.Button(action_frame, text="📅 تقرير يومي",
                                 command=lambda: self.generate_report(section, "daily"),
                                 bg=self.colors['button'], fg=self.colors['button_text'],
                                 font=('Arial', 10))
            daily_btn.pack(side=tk.RIGHT, padx=5)  # التعبئة من اليمين
            
            # تحميل البيانات للقسم
            self.load_section_data(section)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء واجهة القسم: {str(e)}")
    
    def get_currency_symbol(self):
        # إرجاع رمز العملة الحالي
        currency = self.settings.get("currency", "شيكل")
        if currency == "دولار أمريكي":
            return "$"
        elif currency == "ريال سعودي":
            return "ر.س"
        elif currency == "ريال يمني":
            return "ر.ي"
        else:
            return "شيكل"
    
    def show_context_menu(self, event, section):
        # إنشاء قائمة السياق
        try:
            menu = tk.Menu(self.root, tearoff=0)
            menu.add_command(label="تعديل", command=lambda: self.edit_selected_record(section))
            menu.add_command(label="حذف", command=lambda: self.delete_selected_record(section))
            menu.add_separator()
            menu.add_command(label="نسخ", command=lambda: self.copy_selected_record(section))
            
            # تحديد العنصر الذي تم النقر عليه
            tree = self.trees[section]
            item = tree.identify_row(event.y)
            if item:
                tree.selection_set(item)
                menu.post(event.x_root, event.y_root)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")
    
    def copy_selected_record(self, section):
        try:
            tree = self.trees[section]
            selected = tree.selection()
            if not selected:
                messagebox.showwarning("تحذير", "يرجى اختيار سجل لنسخه")
                return
            
            item = selected[0]
            values = tree.item(item, 'values')
            
            # نسخ القيم إلى الحقول
            date_str = values[0]
            self.date_vars[section].set(date_str)
            
            fields = ["الدخل", "الصرفة", "الكهرباء", "تسديد انترنت", "الايجار", "ملاحظات"]
            for i, field in enumerate(fields):
                if field == "ملاحظات":
                    self.entry_vars[section][field].set(values[7] if len(values) > 7 else "")
                else:
                    try:
                        value = float(values[i+1]) if values[i+1] else 0.0
                        self.entry_vars[section][field].set(value)
                    except:
                        self.entry_vars[section][field].set(0.0)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء نسخ السجل: {str(e)}")
    
    def edit_selected_record(self, section):
        try:
            tree = self.trees[section]
            selected = tree.selection()
            if not selected:
                messagebox.showwarning("تحذير", "يرجى اختيار سجل لتعديله")
                return
            
            item = selected[0]
            values = tree.item(item, 'values')
            date_str = values[0]
            
            # فتح نافذة التعديل
            edit_window = tk.Toplevel(self.root)
            edit_window.title("تعديل السجل")
            edit_window.geometry("500x400")
            edit_window.configure(bg=self.colors['bg'])
            edit_window.transient(self.root)
            edit_window.grab_set()
            
            # جعل النافذة في المركز
            edit_window.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() - edit_window.winfo_width()) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - edit_window.winfo_height()) // 2
            edit_window.geometry(f"+{x}+{y}")
            
            header = tk.Label(edit_window, text=f"تعديل سجل قسم {section} - {date_str}", 
                             font=('Arial', 14, 'bold'), bg=self.colors['header'], fg='white', pady=10)
            header.pack(fill='x', pady=(0, 10))
            
            # إطار المحتوى
            content_frame = tk.Frame(edit_window, bg=self.colors['bg'])
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # الحقول للتعديل
            fields = ["الدخل", "الصرفة", "الكهرباء", "تسديد انترنت", "الايجار", "ملاحظات"]
            entry_vars = {}
            
            for i, field in enumerate(fields):
                field_frame = tk.Frame(content_frame, bg=self.colors['bg'])
                field_frame.pack(fill='x', pady=8)
                
                tk.Label(field_frame, text=f"{field}:", bg=self.colors['bg'], 
                        font=('Arial', 10), width=15, anchor='e').pack(side=tk.RIGHT, padx=5)
                
                if field == "ملاحظات":
                    var = tk.StringVar(value=values[7] if len(values) > 7 else "")
                    entry = tk.Entry(field_frame, textvariable=var, font=('Arial', 10))
                    entry_vars[field] = var
                    entry.pack(side=tk.RIGHT, fill='x', expand=True, padx=5)
                else:
                    # الحصول على القيمة من الجدول (العمود i+1)
                    value = values[i+1] if len(values) > i+1 else 0
                    try:
                        num_value = float(value) if value else 0.0
                    except:
                        num_value = 0.0
                        
                    var = tk.DoubleVar(value=num_value)
                    entry = tk.Entry(field_frame, textvariable=var, font=('Arial', 10), justify='right')
                    entry_vars[field] = var
                    entry.pack(side=tk.RIGHT, padx=5)
                    
                    # إضافة علامة العملة للحقول الرقمية
                    currency_label = tk.Label(field_frame, text=self.get_currency_symbol(), bg=self.colors['bg'], font=('Arial', 10))
                    currency_label.pack(side=tk.RIGHT, padx=5)
            
            # أزرار الحفظ والإلغاء (محاذاة لليمين)
            button_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            button_frame.pack(fill='x', pady=20)
            
            save_btn = tk.Button(button_frame, text="💾 حفظ التعديلات", 
                                command=lambda: self.save_edited_record(section, date_str, entry_vars, edit_window),
                                bg=self.colors['button'], fg=self.colors['button_text'],
                                font=('Arial', 10, 'bold'))
            save_btn.pack(side=tk.RIGHT, padx=5)  # التعبئة من اليمين
            
            cancel_btn = tk.Button(button_frame, text="❌ إلغاء", 
                                  command=edit_window.destroy,
                                  bg=self.colors['warning'], fg='white',
                                  font=('Arial', 10))
            cancel_btn.pack(side=tk.RIGHT, padx=5)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحرير السجل: {str(e)}")
    
    def save_edited_record(self, section, date_str, entry_vars, window):
        try:
            # جمع البيانات المعدلة
            data = {
                "الدخل": entry_vars["الدخل"].get(),
                "الصرفة": entry_vars["الصرفة"].get(),
                "الكهرباء": entry_vars["الكهرباء"].get(),
                "تسديد انترنت": entry_vars["تسديد انترنت"].get(),
                "الايجار": entry_vars["الايجار"].get(),
                "ملاحظات": entry_vars["ملاحظات"].get()
            }
            
            # حساب الربح/الخسارة
            income = float(data["الدخل"] or 0)
            expenses = sum([float(data["الصرفة"] or 0), float(data["الكهرباء"] or 0), 
                           float(data["تسديد انترنت"] or 0), float(data["الايجار"] or 0)])
            profit = income - expenses
            
            # حفظ البيانات المعدلة
            self.data[section][date_str] = data
            self.data[section][date_str]["الربح"] = profit
            
            # تحديث الجدول
            self.update_tree(section)
            
            # حفظ البيانات في ملف
            self.save_to_file()
            
            messagebox.showinfo("نجاح", "تم تعديل البيانات بنجاح")
            window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ التعديلات: {str(e)}")
    
    def delete_selected_record(self, section):
        try:
            tree = self.trees[section]
            selected = tree.selection()
            if not selected:
                messagebox.showwarning("تحذير", "يرجى اختيار سجل لحذفه")
                return
            
            item = selected[0]
            values = tree.item(item, 'values')
            date_str = values[0]
            
            if messagebox.askyesno("تأكيد", f"هل أنت متأكد من حذف سجل تاريخ {date_str}؟"):
                if date_str in self.data[section]:
                    del self.data[section][date_str]
                    
                    # إذا كان هذا السجل موجوداً في بيانات اليوم، قم بحذفه منها أيضاً
                    if section in self.today_data and date_str in self.today_data[section]:
                        del self.today_data[section][date_str]
                    
                    # تحديث الجدول
                    self.update_tree(section)
                    
                    # حفظ البيانات في ملف
                    self.save_to_file()
                    
                    messagebox.showinfo("نجاح", "تم حذف البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف السجل: {str(e)}")
    
    def create_report_ui(self):
        try:
            # عنوان التقرير الشامل
            title_frame = tk.Frame(self.report_frame, bg=self.colors['header'])
            title_frame.pack(fill='x', pady=(0, 10))
            
            title_label = tk.Label(title_frame, text="التقرير الشامل لجميع الأقسام", 
                                  font=('Arial', 16, 'bold'), bg=self.colors['header'], fg='white', pady=10)
            title_label.pack()
            
            # إطار خيارات التقرير مع محاذاة يمين
            options_frame = tk.LabelFrame(self.report_frame, text="خيارات التقرير",
                                         font=('Arial', 12, 'bold'), bg=self.colors['bg'], padx=10, pady=10)
            options_frame.pack(fill='x', pady=5, anchor='e')

            # إطار للتحكم في التاريخ
            date_control_frame = tk.Frame(options_frame, bg=self.colors['bg'])
            date_control_frame.pack(side=tk.RIGHT, padx=10)

            self.report_year = tk.StringVar(value=str(date.today().year))
            year_spin = tk.Spinbox(date_control_frame, from_=2020, to=2030, textvariable=self.report_year,
                                  width=5, font=('Arial', 10), justify='center')
            year_spin.pack(side=tk.RIGHT, padx=5)

            tk.Label(date_control_frame, text="السنة:", bg=self.colors['bg'], font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)

            self.report_month = tk.StringVar(value=str(date.today().month))
            month_spin = tk.Spinbox(date_control_frame, from_=1, to=12, textvariable=self.report_month,
                                   width=5, font=('Arial', 10), justify='center')
            month_spin.pack(side=tk.RIGHT, padx=5)

            tk.Label(date_control_frame, text="الشهر:", bg=self.colors['bg'], font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)

            # أزرار التقرير (محاذاة لليمين)
            button_frame = tk.Frame(options_frame, bg=self.colors['bg'])
            button_frame.pack(side=tk.RIGHT, padx=10)

            export_btn = tk.Button(button_frame, text="📤 تصدير إلى Excel", command=self.export_to_excel,
                                  bg=self.colors['button'], fg=self.colors['button_text'],
                                  font=('Arial', 10))
            export_btn.pack(side=tk.RIGHT, padx=5)

            print_btn = tk.Button(button_frame, text="🖨️ طباعة التقرير", command=self.print_comprehensive_report,
                                 bg=self.colors['accent'], fg='white',
                                 font=('Arial', 10))
            print_btn.pack(side=tk.RIGHT, padx=5)

            generate_btn = tk.Button(button_frame, text="📊 توليد التقرير", command=self.generate_comprehensive_report,
                                    bg=self.colors['button'], fg=self.colors['button_text'],
                                    font=('Arial', 10))
            generate_btn.pack(side=tk.RIGHT, padx=5)
            
            # منطقة عرض التقرير
            report_container = tk.LabelFrame(self.report_frame, text="نتيجة التقرير", 
                                            font=('Arial', 12, 'bold'), bg=self.colors['bg'], padx=10, pady=10)
            report_container.pack(fill='both', expand=True, pady=10)
            
            report_text = scrolledtext.ScrolledText(report_container, width=100, height=20, 
                                                   font=('Arial', 10), wrap=tk.WORD)
            report_text.pack(fill='both', expand=True)
            self.report_text = report_text
            
            # إطار للرسوم البيانية (إذا كان matplotlib متاحاً)
            if HAS_MATPLOTLIB:
                chart_frame = tk.Frame(report_container, bg=self.colors['bg'])
                chart_frame.pack(fill='both', expand=True, pady=10)
                self.chart_frame = chart_frame
            else:
                no_chart_label = tk.Label(report_container, text="الرسوم البيانية غير متاحة (matplotlib غير مثبت)", 
                                         bg=self.colors['bg'], fg='red', font=('Arial', 10))
                no_chart_label.pack(pady=10)
                self.chart_frame = None
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء واجهة التقرير: {str(e)}")
    
    def create_settings_ui(self):
        try:
            # عنوان الإعدادات
            title_frame = tk.Frame(self.settings_frame, bg=self.colors['header'])
            title_frame.pack(fill='x', pady=(0, 10))
            
            title_label = tk.Label(title_frame, text="إعدادات البرنامج", 
                                  font=('Arial', 16, 'bold'), bg=self.colors['header'], fg='white', pady=10)
            title_label.pack()
            
            # إطار الإعدادات العامة مع محاذاة يمين
            general_frame = tk.LabelFrame(self.settings_frame, text="الإعدادات العامة",
                                         font=('Arial', 12, 'bold'), bg=self.colors['bg'], padx=10, pady=10)
            general_frame.pack(fill='x', pady=5, anchor='e')

            # الحفظ التلقائي
            auto_save_frame = tk.Frame(general_frame, bg=self.colors['bg'])
            auto_save_frame.pack(fill='x', pady=5, anchor='e')

            self.auto_save_var = tk.BooleanVar(value=self.settings["auto_save"])
            auto_save_cb = tk.Checkbutton(auto_save_frame, text="الحفظ التلقائي للبيانات",
                                         variable=self.auto_save_var, command=self.toggle_auto_save,
                                         bg=self.colors['bg'], font=('Arial', 10))
            auto_save_cb.pack(anchor='e')

            # النسخ احتياطي
            backup_frame = tk.Frame(general_frame, bg=self.colors['bg'])
            backup_frame.pack(fill='x', pady=5, anchor='e')

            self.backup_var = tk.BooleanVar(value=self.settings["backup_on_exit"])
            backup_cb = tk.Checkbutton(backup_frame, text="إنشاء نسخة احتياطية عند الخروج",
                                      variable=self.backup_var, command=self.toggle_backup,
                                      bg=self.colors['bg'], font=('Arial', 10))
            backup_cb.pack(anchor='e')

            # إعدادات العملة
            currency_frame = tk.Frame(general_frame, bg=self.colors['bg'])
            currency_frame.pack(fill='x', pady=5, anchor='e')

            currencies = ["شيكل", "دولار أمريكي", "ريال سعودي", "ريال يمني"]
            self.currency_var = tk.StringVar(value=self.settings["currency"])
            currency_combo = ttk.Combobox(currency_frame, textvariable=self.currency_var,
                                         values=currencies, state="readonly",
                                         font=('Arial', 10), width=15)
            currency_combo.pack(side=tk.RIGHT, padx=5)
            currency_combo.bind("<<ComboboxSelected>>", self.change_currency)

            tk.Label(currency_frame, text="العملة:", bg=self.colors['bg'],
                    font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)
            
            # إعدادات الطباعة مع محاذاة يمين
            printer_frame = tk.LabelFrame(self.settings_frame, text="إعدادات الطباعة",
                                         font=('Arial', 12, 'bold'), bg=self.colors['bg'], padx=10, pady=10)
            printer_frame.pack(fill='x', pady=5, anchor='e')

            # اختيار الطابعة
            printer_select_frame = tk.Frame(printer_frame, bg=self.colors['bg'])
            printer_select_frame.pack(fill='x', pady=5, anchor='e')

            # الحصول على الطابعات المتاحة (محاكاة)
            available_printers = ["الطابعة الافتراضية", "طابعة المكتب", "طابعة PDF", "طابعة الشبكة"]

            self.printer_var = tk.StringVar(value=self.settings["printer"])
            printer_combo = ttk.Combobox(printer_select_frame, textvariable=self.printer_var,
                                        values=available_printers, state="readonly",
                                        font=('Arial', 10), width=20)
            printer_combo.pack(side=tk.RIGHT, padx=5)
            printer_combo.bind("<<ComboboxSelected>>", self.change_printer)

            tk.Label(printer_select_frame, text="الطابعة:", bg=self.colors['bg'],
                    font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)

            # أزرار الإجراءات (محاذاة لليمين)
            action_frame = tk.Frame(self.settings_frame, bg=self.colors['bg'])
            action_frame.pack(fill='x', pady=20, anchor='e')

            backup_btn = tk.Button(action_frame, text="📂 نسخ احتياطي الآن", command=self.backup_data,
                                  bg=self.colors['button'], fg=self.colors['button_text'],
                                  font=('Arial', 10))
            backup_btn.pack(side=tk.RIGHT, padx=5)

            default_btn = tk.Button(action_frame, text="⚙️ الإعدادات الافتراضية", command=self.reset_settings,
                                   bg=self.colors['accent'], fg='white',
                                   font=('Arial', 10))
            default_btn.pack(side=tk.RIGHT, padx=5)

            save_btn = tk.Button(action_frame, text="💾 حفظ الإعدادات", command=self.save_settings,
                                bg=self.colors['button'], fg=self.colors['button_text'],
                                font=('Arial', 10, 'bold'))
            save_btn.pack(side=tk.RIGHT, padx=5)  # التعبئة من اليمين
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء واجهة الإعدادات: {str(e)}")
    
    def change_currency(self, event):
        self.settings["currency"] = self.currency_var.get()
        # تحديث الواجهة لتعكس العملة الجديدة
        self.update_all_currency_displays()
        # حفظ الإعدادات
        self.save_settings()
        messagebox.showinfo("تم التحديث", f"تم تغيير العملة إلى {self.settings['currency']} في جميع أنحاء البرنامج")

    def update_all_currency_displays(self):
        """تحديث عرض العملة في جميع أنحاء البرنامج"""
        try:
            # تحديث جميع الأشجار
            for section in self.data.keys():
                if hasattr(self, 'trees') and section in self.trees:
                    self.update_tree(section)

            # تحديث جميع تسميات العملة في واجهات الأقسام
            for section in self.data.keys():
                if hasattr(self, 'frames') and section in self.frames:
                    self.update_currency_labels_in_section(section)

            # إذا كان هناك بيانات اليوم، قم بتحديثها أيضًا
            if hasattr(self, 'today_data'):
                for section in self.today_data.keys():
                    if hasattr(self, 'trees') and section in self.trees:
                        self.update_tree_with_today_data(section)
        except Exception as e:
            print(f"خطأ في تحديث عرض العملة: {str(e)}")

    def update_currency_labels_in_section(self, section):
        """تحديث تسميات العملة في قسم معين"""
        try:
            frame = self.frames[section]
            currency_symbol = self.get_currency_symbol()

            # البحث عن جميع تسميات العملة وتحديثها
            for widget in frame.winfo_children():
                self.update_currency_labels_recursive(widget, currency_symbol)
        except Exception as e:
            print(f"خطأ في تحديث تسميات العملة للقسم {section}: {str(e)}")

    def update_currency_labels_recursive(self, widget, currency_symbol):
        """تحديث تسميات العملة بشكل تكراري في جميع العناصر الفرعية"""
        try:
            # إذا كان العنصر تسمية وتحتوي على رمز عملة قديم
            if isinstance(widget, tk.Label):
                current_text = widget.cget("text")
                old_symbols = ["شيكل", "$", "ر.س", "ر.ي"]
                for old_symbol in old_symbols:
                    if old_symbol in current_text and old_symbol != currency_symbol:
                        widget.config(text=currency_symbol)
                        break

            # تكرار العملية للعناصر الفرعية
            for child in widget.winfo_children():
                self.update_currency_labels_recursive(child, currency_symbol)
        except Exception as e:
            pass  # تجاهل الأخطاء في العناصر التي لا تدعم winfo_children
    
    def toggle_backup(self):
        self.settings["backup_on_exit"] = self.backup_var.get()
        self.save_settings()
    
    def change_printer(self, event):
        self.settings["printer"] = self.printer_var.get()
        self.save_settings()
    
    def reset_settings(self):
        self.settings = {
            "auto_save": True,
            "backup_on_exit": True,
            "printer": "الطابعة الافتراضية",
            "currency": "شيكل"
        }
        self.auto_save_var.set(True)
        self.backup_var.set(True)
        self.printer_var.set("الطابعة الافتراضية")
        self.currency_var.set("شيكل")
        self.save_settings()
        messagebox.showinfo("نجاح", "تم استعادة الإعدادات الافتراضية")
    
    def show_comprehensive_report(self):
        # تحديد تبويب التقرير الشامل
        self.notebook.select(len(self.data))
    
    def choose_date(self, section):
        def set_date():
            selected_date = cal.selection_get()
            self.date_vars[section].set(selected_date.strftime("%Y-%m-%d"))
            top.destroy()
        
        try:
            top = tk.Toplevel(self.root)
            top.title("اختر تاريخ")
            top.geometry("300x300")
            top.transient(self.root)
            top.grab_set()
            
            # جعل النافذة في المركز
            top.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() - top.winfo_width()) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - top.winfo_height()) // 2
            top.geometry(f"+{x}+{y}")
            
            # استخدام التقويم المبسط
            cal = SimpleCalendar(top)
            cal.pack(pady=10)
            
            btn_frame = tk.Frame(top)
            btn_frame.pack(pady=10)
            
            ok_btn = tk.Button(btn_frame, text="موافق", command=set_date, 
                              bg=self.colors['button'], fg=self.colors['button_text'],
                              font=('Arial', 10))
            ok_btn.pack(side=tk.RIGHT, padx=10)
            
            cancel_btn = tk.Button(btn_frame, text="إلغاء", command=top.destroy,
                                  bg=self.colors['warning'], fg='white',
                                  font=('Arial', 10))
            cancel_btn.pack(side=tk.RIGHT, padx=10)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح التقويم: {str(e)}")
    
    def save_data(self, section):
        try:
            date_str = self.date_vars[section].get()
            
            # التحقق من إدخال التاريخ
            if not date_str:
                messagebox.showerror("خطأ", "يجب إدخال التاريخ")
                return
            
            # جمع البيانات
            data = {
                "الدخل": self.entry_vars[section]["الدخل"].get(),
                "الصرفة": self.entry_vars[section]["الصرفة"].get(),
                "الكهرباء": self.entry_vars[section]["الكهرباء"].get(),
                "تسديد انترنت": self.entry_vars[section]["تسديد انترنت"].get(),
                "الايجار": self.entry_vars[section]["الايجار"].get(),
                "ملاحظات": self.entry_vars[section]["ملاحظات"].get()
            }
            
            # التحقق من إدخال البيانات
            if not any([data["الدخل"], data["الصرفة"], data["الكهرباء"], data["تسديد انترنت"], data["الايجار"]]):
                messagebox.showwarning("تحذير", "يجب إدخال قيمة واحدة على الأقل")
                return
            
            # حساب الربح/الخسارة
            income = float(data["الدخل"] or 0)
            expenses = sum([float(data["الصرفة"] or 0), float(data["الكهرباء"] or 0), 
                           float(data["تسديد انترنت"] or 0), float(data["الايجار"] or 0)])
            profit = income - expenses
            
            # حفظ البيانات في بيانات اليوم (تراكمي)
            if section not in self.today_data:
                self.today_data[section] = {}
            
            # استخدام الطابع الزمني كمعرف فريد لكل إدخال
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.today_data[section][timestamp] = data
            self.today_data[section][timestamp]["الربح"] = profit
            
            # تحديث الجدول لعرض البيانات التراكمية
            self.update_tree_with_today_data(section)
            
            # حفظ البيانات تلقائياً إذا كان الخيار مفعلاً
            if self.settings["auto_save"]:
                self.save_to_file()
            
            messagebox.showinfo("نجاح", "تم حفظ البيانات بنجاح")
            self.clear_entries(section)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def update_tree_with_today_data(self, section):
        try:
            tree = self.trees[section]

            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # الحصول على رمز العملة الحالي
            currency_symbol = self.get_currency_symbol()

            # إضافة بيانات اليوم التراكمية أولاً مع رمز العملة
            if section in self.today_data:
                for timestamp, data in self.today_data[section].items():
                    # تنسيق القيمة لتعرض "خسارة" إذا كانت سالبة مع رمز العملة
                    profit = data['الربح']
                    profit_text = f"{profit:.2f} {currency_symbol}" if profit >= 0 else f"خسارة: {abs(profit):.2f} {currency_symbol}"

                    tree.insert("", "end", values=(
                        timestamp.split()[0],  # التاريخ فقط
                        f"{data.get('الدخل', 0):.2f} {currency_symbol}",
                        f"{data.get('الصرفة', 0):.2f} {currency_symbol}",
                        f"{data.get('الكهرباء', 0):.2f} {currency_symbol}",
                        f"{data.get('تسديد انترنت', 0):.2f} {currency_symbol}",
                        f"{data.get('الايجار', 0):.2f} {currency_symbol}",
                        profit_text,
                        data.get("ملاحظات", "")
                    ))

            # ثم إضافة البيانات التاريخية مع رمز العملة
            for date_str, data in self.data[section].items():
                # تنسيق القيمة لتعرض "خسارة" إذا كانت سالبة مع رمز العملة
                profit = data['الربح']
                profit_text = f"{profit:.2f} {currency_symbol}" if profit >= 0 else f"خسارة: {abs(profit):.2f} {currency_symbol}"

                tree.insert("", "end", values=(
                    date_str,
                    f"{data.get('الدخل', 0):.2f} {currency_symbol}",
                    f"{data.get('الصرفة', 0):.2f} {currency_symbol}",
                    f"{data.get('الكهرباء', 0):.2f} {currency_symbol}",
                    f"{data.get('تسديد انترنت', 0):.2f} {currency_symbol}",
                    f"{data.get('الايجار', 0):.2f} {currency_symbol}",
                    profit_text,
                    data.get("ملاحظات", "")
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الجدول: {str(e)}")
    
    def clear_entries(self, section):
        for field in self.entry_vars[section]:
            if field == "ملاحظات":
                self.entry_vars[section][field].set("")
            else:
                self.entry_vars[section][field].set(0.0)
    
    def update_tree(self, section):
        try:
            tree = self.trees[section]

            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # الحصول على رمز العملة الحالي
            currency_symbol = self.get_currency_symbol()

            # إضافة البيانات الجديدة مع رمز العملة
            for date_str, data in self.data[section].items():
                # تنسيق القيمة لتعرض "خسارة" إذا كانت سالبة مع رمز العملة
                profit = data['الربح']
                profit_text = f"{profit:.2f} {currency_symbol}" if profit >= 0 else f"خسارة: {abs(profit):.2f} {currency_symbol}"

                tree.insert("", "end", values=(
                    date_str,
                    f"{data.get('الدخل', 0):.2f} {currency_symbol}",
                    f"{data.get('الصرفة', 0):.2f} {currency_symbol}",
                    f"{data.get('الكهرباء', 0):.2f} {currency_symbol}",
                    f"{data.get('تسديد انترنت', 0):.2f} {currency_symbol}",
                    f"{data.get('الايجار', 0):.2f} {currency_symbol}",
                    profit_text,
                    data.get("ملاحظات", "")
                ))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الجدول: {str(e)}")
    
    def load_section_data(self, section):
        self.update_tree(section)
    
    def generate_report(self, section, report_type):
        try:
            currency_symbol = self.get_currency_symbol()
            report = f"تقرير {report_type} لقسم {section}\n"
            report += "="*50 + "\n\n"
            
            total_income = 0
            total_expenses = 0
            total_profit = 0
            
            if report_type == "daily":
                # تقرير يومي للتاريخ المحدد
                date_str = self.date_vars[section].get()
                if date_str in self.data[section]:
                    data = self.data[section][date_str]
                    report += f"التاريخ: {date_str}\n"
                    report += f"الدخل: {data.get('الدخل', 0):.2f} {currency_symbol}\n"
                    report += f"الصرفة: {data.get('الصرفة', 0):.2f} {currency_symbol}\n"
                    report += f"الكهرباء: {data.get('الكهرباء', 0):.2f} {currency_symbol}\n"
                    report += f"تسديد انترنت: {data.get('تسديد انترنت', 0):.2f} {currency_symbol}\n"
                    report += f"الايجار: {data.get('الايجار', 0):.2f} {currency_symbol}\n"
                    
                    # عرض "خسارة" بدلاً من القيمة السالبة
                    profit = data.get('الربح', 0)
                    if profit >= 0:
                        report += f"صافي الربح: {profit:.2f} {currency_symbol}\n"
                    else:
                        report += f"صافي الخسارة: {abs(profit):.2f} {currency_symbol}\n"
                        
                    report += f"ملاحظات: {data.get('ملاحظات', '')}\n"
                else:
                    report += "لا توجد بيانات لهذا التاريخ\n"
            
            else:
                # تقرير شهري
                year = date.today().year
                month = date.today().month
                
                for date_str, data in self.data[section].items():
                    try:
                        report_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                        if report_date.year == year and report_date.month == month:
                            total_income += float(data.get('الدخل', 0) or 0)
                            total_expenses += sum([
                                float(data.get('الصرفة', 0) or 0),
                                float(data.get('الكهرباء', 0) or 0),
                                float(data.get('تسديد انترنت', 0) or 0),
                                float(data.get('الايجار', 0) or 0)
                            ])
                            total_profit += float(data.get('الربح', 0) or 0)
                    except:
                        continue
                
                report += f"الشهر: {month}/{year}\n"
                report += f"إجمالي الدخل: {total_income:.2f} {currency_symbol}\n"
                report += f"إجمالي المصروفات: {total_expenses:.2f} {currency_symbol}\n"
                
                # عرض "خسارة" بدلاً من القيمة السالبة
                if total_profit >= 0:
                    report += f"صافي الربح: {total_profit:.2f} {currency_symbol}\n"
                else:
                    report += f"صافي الخسارة: {abs(total_profit):.2f} {currency_symbol}\n"
            
            # عرض التقرير في نافذة منبثقة
            self.show_report_window(report, f"تقرير {section}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
    
    def generate_comprehensive_report(self):
        try:
            year = int(self.report_year.get())
            month = int(self.report_month.get())
        except:
            messagebox.showerror("خطأ", "يرجى إدخال سنة وشهر صحيحين")
            return
        
        try:
            currency_symbol = self.get_currency_symbol()
            report = f"التقرير الشامل لجميع الأقسام - {month}/{year}\n"
            report += "="*60 + "\n\n"
            
            total_income = 0
            total_expenses = 0
            total_profit = 0
            
            section_totals = {}
            
            for section in self.data.keys():
                section_income = 0
                section_expenses = 0
                section_profit = 0
                
                for date_str, data in self.data[section].items():
                    try:
                        report_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                        if report_date.year == year and report_date.month == month:
                            section_income += float(data.get('الدخل', 0) or 0)
                            section_expenses += sum([
                                float(data.get('الصرفة', 0) or 0),
                                float(data.get('الكهرباء', 0) or 0),
                                float(data.get('تسديد انترنت', 0) or 0),
                                float(data.get('الايجار', 0) or 0)
                            ])
                            section_profit += float(data.get('الربح', 0) or 0)
                    except:
                        continue
                
                report += f"قسم {section}:\n"
                report += f"  الدخل: {section_income:.2f} {currency_symbol}\n"
                report += f"  المصروفات: {section_expenses:.2f} {currency_symbol}\n"
                
                # عرض "خسارة" بدلاً من القيمة السالبة
                if section_profit >= 0:
                    report += f"  الربح: {section_profit:.2f} {currency_symbol}\n\n"
                else:
                    report += f"  الخسارة: {abs(section_profit):.2f} {currency_symbol}\n\n"
                
                total_income += section_income
                total_expenses += section_expenses
                total_profit += section_profit
                
                section_totals[section] = {
                    'income': section_income,
                    'expenses': section_expenses,
                    'profit': section_profit
                }
            
            report += "الإجمالي العام:\n"
            report += f"إجمالي الدخل: {total_income:.2f} {currency_symbol}\n"
            report += f"إجمالي المصروفات: {total_expenses:.2f} {currency_symbol}\n"
            
            # عرض "خسارة" بدلاً من القيمة السالبة
            if total_profit >= 0:
                report += f"صافي الربح: {total_profit:.2f} {currency_symbol}\n"
            else:
                report += f"صادي الخسارة: {abs(total_profit):.2f} {currency_symbol}\n"
            
            # عرض التقرير
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            # إنشاء رسم بياني (إذا كان matplotlib متاحاً)
            if HAS_MATPLOTLIB and self.chart_frame:
                self.create_charts(section_totals, total_income, total_expenses, total_profit)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير الشامل: {str(e)}")
    
    def generate_monthly_report(self):
        # توليد تقرير شهري لجميع الأقسام
        current_date = date.today()
        self.report_year.set(str(current_date.year))
        self.report_month.set(str(current_date.month))
        self.generate_comprehensive_report()
        self.show_comprehensive_report()
    
    def generate_yearly_report(self):
        # توليد تقرير سنوي لجميع الأقسام
        try:
            year = date.today().year
            currency_symbol = self.get_currency_symbol()
            report = f"التقرير السنوي لجميع الأقسام - {year}\n"
            report += "="*60 + "\n\n"
            
            total_income = 0
            total_expenses = 0
            total_profit = 0
            
            section_totals = {}
            
            for section in self.data.keys():
                section_income = 0
                section_expenses = 0
                section_profit = 0
                
                for date_str, data in self.data[section].items():
                    try:
                        report_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                        if report_date.year == year:
                            section_income += float(data.get('الدخل', 0) or 0)
                            section_expenses += sum([
                                float(data.get('الصرفة', 0) or 0),
                                float(data.get('الكهرباء', 0) or 0),
                                float(data.get('تسديد انترنت', 0) or 0),
                                float(data.get('الايجار', 0) or 0)
                            ])
                            section_profit += float(data.get('الربح', 0) or 0)
                    except:
                        continue
                
                report += f"قسم {section}:\n"
                report += f"  الدخل: {section_income:.2f} {currency_symbol}\n"
                report += f"  المصروفات: {section_expenses:.2f} {currency_symbol}\n"
                
                # عرض "خسارة" بدلاً من القيمة السالبة
                if section_profit >= 0:
                    report += f"  الربح: {section_profit:.2f} {currency_symbol}\n\n"
                else:
                    report += f"  الخسارة: {abs(section_profit):.2f} {currency_symbol}\n\n"
                
                total_income += section_income
                total_expenses += section_expenses
                total_profit += section_profit
                
                section_totals[section] = {
                    'income': section_income,
                    'expenses': section_expenses,
                    'profit': section_profit
                }
            
            report += "الإجمالي العام:\n"
            report += f"إجمالي الدخل: {total_income:.2f} {currency_symbol}\n"
            report += f"إجمالي المصروفات: {total_expenses:.2f} {currency_symbol}\n"
            
            # عرض "خسارة" بدلاً من القيمة السالبة
            if total_profit >= 0:
                report += f"صادي الربح: {total_profit:.2f} {currency_symbol}\n"
            else:
                report += f"صادي الخسارة: {abs(total_profit):.2f} {currency_symbol}\n"
            
            # عرض التقرير في نافذة منبثقة
            self.show_report_window(report, f"تقرير سنوي - {year}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير السنوي: {str(e)}")
    
    def create_charts(self, section_totals, total_income, total_expenses, total_profit):
        try:
            # مسح الإطارات السابقة
            for widget in self.chart_frame.winfo_children():
                widget.destroy()
            
            # إنشاء رسوم بيانية
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # الرسم البياني الأول: أرباح/خسائر الأقسام
            sections = list(section_totals.keys())
            profits = [section_totals[section]['profit'] for section in sections]
            
            # استخدام ألوان مختلفة للأرباح (أخضر) والخسائر (أحمر)
            colors = ['#2e8b57' if p >= 0 else '#dc143c' for p in profits]
            
            bars = ax1.bar(sections, profits, color=colors)
            ax1.set_title('أرباح/خسائر الأقسام')
            ax1.set_ylabel('المبلغ')
            
            # إضافة القيم فوق الأعمدة
            for bar, profit in zip(bars, profits):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{profit:.2f}', ha='center', va='bottom' if profit >= 0 else 'top')
            
            # الرسم البياني الثاني: توزيع الإيرادات والمصروفات
            labels = ['الدخل', 'المصروفات']
            values = [total_income, total_expenses]
            colors = ['#2e8b57', '#dc143c']
            
            ax2.pie(values, labels=labels, colors=colors, autopct='%1.1f%%')
            ax2.set_title('توزيع الإيرادات والمصروفات')
            
            # عرض الرسوم البيانية في الواجهة
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الرسوم البيانية: {str(e)}")
    
    def show_report_window(self, report, title):
        try:
            top = tk.Toplevel(self.root)
            top.title(title)
            top.geometry("600x500")
            top.configure(bg=self.colors['bg'])
            top.transient(self.root)
            
            # جعل النافذة في المركز
            top.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() - top.winfo_width()) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - top.winfo_height()) // 2
            top.geometry(f"+{x}+{y}")
            
            # إطار العنوان
            title_frame = tk.Frame(top, bg=self.colors['header'])
            title_frame.pack(fill='x', pady=(0, 10))
            
            title_label = tk.Label(title_frame, text=title, font=('Arial', 14, 'bold'), 
                                  bg=self.colors['header'], fg='white', pady=5)
            title_label.pack()
            
            # منطقة النص
            text_frame = tk.Frame(top, bg=self.colors['bg'])
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            text_area = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, width=70, height=20, 
                                                 font=('Arial', 10))
            text_area.pack(fill='both', expand=True)
            text_area.insert(1.0, report)
            text_area.config(state=tk.DISABLED)
            
            # أزرار الإجراءات (محاذاة لليمين)
            button_frame = tk.Frame(top, bg=self.colors['bg'])
            button_frame.pack(fill='x', pady=10)
            
            print_btn = tk.Button(button_frame, text="🖨️ طباعة", command=lambda: self.print_text(report),
                                 bg=self.colors['button'], fg=self.colors['button_text'],
                                 font=('Arial', 10))
            print_btn.pack(side=tk.RIGHT, padx=10)  # التعبئة من اليمين
            
            export_btn = tk.Button(button_frame, text="💾 حفظ كملف", command=lambda: self.save_report_to_file(report, title),
                                  bg=self.colors['accent'], fg='white',
                                  font=('Arial', 10))
            export_btn.pack(side=tk.RIGHT, padx=10)
            
            close_btn = tk.Button(button_frame, text="إغلاق", command=top.destroy,
                                 bg=self.colors['warning'], fg='white',
                                 font=('Arial', 10))
            close_btn.pack(side=tk.RIGHT, padx=10)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض نافذة التقرير: {str(e)}")
    
    def save_report_to_file(self, report, title):
        # حفظ التقرير كملف نصي
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("ملف نصي", "*.txt"), ("كل الملفات", "*.*")],
                title="حفظ التقرير",
                initialfile=f"{title}.txt"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                messagebox.showinfo("نجاح", f"تم حفظ التقرير في: {file_path}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الملف: {str(e)}")
    
    def print_report_dialog(self, section=None):
        # عرض حوار الطباعة
        try:
            if section:
                # طباعة تقرير القسم
                currency_symbol = self.get_currency_symbol()
                report = f"تقرير قسم {section}\n"
                report += "="*30 + "\n\n"
                
                for date_str, data in self.data[section].items():
                    report += f"التاريخ: {date_str}\n"
                    report += f"الدخل: {data.get('الدخل', 0):.2f} {currency_symbol}\n"
                    report += f"الصرفة: {data.get('الصرفة', 0):.2f} {currency_symbol}\n"
                    report += f"الكهرباء: {data.get('الكهرباء', 0):.2f} {currency_symbol}\n"
                    report += f"تسديد انترنت: {data.get('تسديد انترنت', 0):.2f} {currency_symbol}\n"
                    report += f"الايجار: {data.get('الايجار', 0):.2f} {currency_symbol}\n"
                    
                    profit = data.get('الربح', 0)
                    if profit >= 0:
                        report += f"صافي الربح: {profit:.2f} {currency_symbol}\n"
                    else:
                        report += f"صافي الخسارة: {abs(profit):.2f} {currency_symbol}\n"
                        
                    report += f"ملاحظات: {data.get('ملاحظات', '')}\n\n"
            else:
                # طباعة التقرير الشامل
                report = self.report_text.get(1.0, tk.END)
            
            # عرض خيارات الطباعة
            self.show_print_options(report)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحضير التقرير للطباعة: {str(e)}")
    
    def show_print_options(self, report):
        # نافذة خيارات الطباعة
        try:
            print_window = tk.Toplevel(self.root)
            print_window.title("خيارات الطباعة")
            print_window.geometry("500x300")
            print_window.configure(bg=self.colors['bg'])
            print_window.transient(self.root)
            print_window.grab_set()
            
            # جعل النافذة في المركز
            print_window.update_idletasks()
            x = self.root.winfo_x() + (self.root.winfo_width() - print_window.winfo_width()) // 2
            y = self.root.winfo_y() + (self.root.winfo_height() - print_window.winfo_height()) // 2
            print_window.geometry(f"+{x}+{y}")
            
            header = tk.Label(print_window, text="خيارات الطباعة", font=('Arial', 16, 'bold'), 
                             bg=self.colors['header'], fg='white', pady=10)
            header.pack(fill='x', pady=(0, 10))
            
            # إطار المحتوى
            content_frame = tk.Frame(print_window, bg=self.colors['bg'])
            content_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            # اختيار الطابعة
            printer_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            printer_frame.pack(fill='x', pady=10)
            
            tk.Label(printer_frame, text="الطابعة:", bg=self.colors['bg'], 
                    font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)
            
            # الحصول على الطابعات المتاحة (محاكاة)
            available_printers = ["الطابعة الافتراضية", "طابعة المكتب", "طابعة PDF", "طابعة الشبكة"]
            
            printer_var = tk.StringVar(value=self.settings["printer"])
            printer_combo = ttk.Combobox(printer_frame, textvariable=printer_var, 
                                        values=available_printers, state="readonly",
                                        font=('Arial', 10), width=20)
            printer_combo.pack(side=tk.RIGHT, padx=5)
            
            # عدد النسخ
            copies_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            copies_frame.pack(fill='x', pady=10)
            
            tk.Label(copies_frame, text="عدد النسخ:", bg=self.colors['bg'], 
                    font=('Arial', 10)).pack(side=tk.RIGHT, padx=5)
            
            copies_var = tk.IntVar(value=1)
            copies_spin = tk.Spinbox(copies_frame, from_=1, to=10, textvariable=copies_var, 
                                    width=5, font=('Arial', 10), justify='center')
            copies_spin.pack(side=tk.RIGHT, padx=5)
            
            # معاينة قبل الطباعة
            preview_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            preview_frame.pack(fill='x', pady=10)
            
            preview_var = tk.BooleanVar(value=True)
            preview_cb = tk.Checkbutton(preview_frame, text="معاينة قبل الطباعة", 
                                       variable=preview_var, bg=self.colors['bg'], 
                                       font=('Arial', 10))
            preview_cb.pack(anchor='w')
            
            # أزرار الإجراءات (محاذاة لليمين)
            button_frame = tk.Frame(content_frame, bg=self.colors['bg'])
            button_frame.pack(fill='x', pady=20)
            
            print_btn = tk.Button(button_frame, text="🖨️ طباعة", 
                                 command=lambda: self.print_text(report, copies_var.get(), print_window),
                                 bg=self.colors['button'], fg=self.colors['button_text'],
                                 font=('Arial', 10, 'bold'))
            print_btn.pack(side=tk.RIGHT, padx=10)  # التعبئة من اليمين
            
            preview_btn = tk.Button(button_frame, text="👁️ معاينة", 
                                   command=lambda: self.show_report_window(report, "معاينة الطباعة"),
                                   bg=self.colors['accent'], fg='white',
                                   font=('Arial', 10))
            preview_btn.pack(side=tk.RIGHT, padx=10)
            
            cancel_btn = tk.Button(button_frame, text="إلغاء", command=print_window.destroy,
                                  bg=self.colors['warning'], fg='white',
                                  font=('Arial', 10))
            cancel_btn.pack(side=tk.RIGHT, padx=10)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح نافذة خيارات الطباعة: {str(e)}")
    
    def print_text(self, text, copies=1, window=None):
        try:
            # إنشاء ملف مؤقت للطباعة
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as f:
                for i in range(copies):
                    f.write(text)
                    if i < copies - 1:
                        f.write("\n\n" + "="*50 + "\n\n")  # فاصل بين النسخ
                temp_file = f.name
            
            # محاولة الطباعة باستخدام الأمر المناسب لنظام التشغيل
            printer = self.settings["printer"]
            if os.name == 'nt':  # Windows
                if printer == "طابعة PDF":
                    # محاكاة حفظ كملف PDF
                    pdf_file = filedialog.asksaveasfilename(
                        defaultextension=".pdf",
                        filetypes=[("ملف PDF", "*.pdf")],
                        title="حفظ كملف PDF"
                    )
                    if pdf_file:
                        with open(pdf_file, 'w', encoding='utf-8') as f:
                            f.write("PDF Content: \n" + text)
                        messagebox.showinfo("نجاح", f"تم حفظ الملف PDF: {pdf_file}")
                else:
                    os.startfile(temp_file, 'print')
            elif os.name == 'posix':  # Linux or macOS
                subprocess.run(['lpr', temp_file])
            
            messagebox.showinfo("طباعة", f"تم إرسال {copies} نسخة إلى {printer}")
            
            if window:
                window.destroy()
            
            # حذف الملف المؤقت بعد فترة
            self.root.after(5000, lambda: os.unlink(temp_file))
            
        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"حدث خطأ أثناء محاولة الطباعة: {str(e)}")
    
    def print_comprehensive_report(self):
        # الحصول على النص من منطقة التقرير
        report = self.report_text.get(1.0, tk.END)
        
        # طباعة التقرير
        self.print_text(report)
    
    def export_to_excel(self):
        # تصدير البيانات إلى Excel (محاكاة)
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("كل الملفات", "*.*")],
                title="تصدير إلى Excel",
                initialfile="تقرير_المحاسبة.xlsx"
            )
            
            if file_path:
                # محاكاة عملية التصدير
                messagebox.showinfo("تصدير", f"تم تصدير البيانات إلى: {file_path}\n\nملاحظة: هذه ميزة محاكاة، تحتاج إلى تثبيت مكتبة openpyxl أو pandas لتنفيذها فعلياً.")
                
                # في التنفيذ الفعلي، نستخدم:
                # import pandas as pd
                # df = pd.DataFrame.from_dict(self.data)
                # df.to_excel(file_path, index=False)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")
    
    def export_section_data(self, section):
        # تصدير بيانات قسم معين
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("كل الملفات", "*.*")],
                title=f"تصدير بيانات {section}",
                initialfile=f"{section}_data.csv"
            )
            
            if file_path:
                # محاكاة عملية التصدير
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("التاريخ,الدخل,الصرفة,الكهرباء,تسديد انترنت,الايجار,الربح,ملاحظات\n")
                    for date_str, data in self.data[section].items():
                        f.write(f"{date_str},{data.get('الدخل', 0)},{data.get('الصرفة', 0)},{data.get('الكهرباء', 0)},{data.get('تسديد انترنت', 0)},{data.get('الايجار', 0)},{data.get('الربح', 0)},{data.get('ملاحظات', '')}\n")
                
                messagebox.showinfo("تصدير", f"تم تصدير بيانات {section} إلى: {file_path}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")
    
    def open_file(self):
        # فتح ملف بيانات
        try:
            file_path = filedialog.askopenfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("كل الملفات", "*.*")],
                title="فتح ملف البيانات"
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                
                # تحديث الواجهة
                self.update_notebook()
                messagebox.showinfo("نجاح", "تم تحميل البيانات بنجاح")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الملف: {str(e)}")
    
    def save_to_file(self):
        # حفظ البيانات إلى ملف
        try:
            with open('accounting_data.json', 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=4)
            
            # تحديث حالة الحفظ
            self.save_status.config(text="تم الحفظ: " + datetime.now().strftime("%H:%M:%S"))
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
            return False
    
    def save_as_file(self):
        # حفظ البيانات باسم جديد
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("كل الملفات", "*.*")],
                title="حفظ البيانات باسم",
                initialfile="accounting_data.json"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=4)
                
                messagebox.showinfo("نجاح", f"تم حفظ البيانات في: {file_path}")
                return True
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الملف: {str(e)}")
            return False
    
    def load_data(self):
        # تحميل البيانات من ملف
        try:
            if os.path.exists('accounting_data.json'):
                with open('accounting_data.json', 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def load_settings(self):
        # تحميل الإعدادات من ملف
        try:
            if os.path.exists('accounting_settings.json'):
                with open('accounting_settings.json', 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
        except:
            # استخدام الإعدادات الافتراضية إذا كان الملف غير موجود
            self.settings = {
                "auto_save": True,
                "backup_on_exit": True,
                "printer": "الطابعة الافتراضية",
                "currency": "شيكل"
            }
    
    def save_settings(self):
        # حفظ الإعدادات إلى ملف
        try:
            with open('accounting_settings.json', 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")
            return False
    
    def backup_data(self):
        # إنشاء نسخة احتياطية
        try:
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"accounting_backup_{timestamp}.json")
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=4)
            
            messagebox.showinfo("نسخ احتياطي", f"تم إنشاء نسخة احتياطية في: {backup_file}")
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")
            return False
    
    def restore_data(self):
        # استعادة بيانات من نسخة احتياطية
        try:
            file_path = filedialog.askopenfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("كل الملفات", "*.*")],
                title="استعادة من نسخة احتياطية",
                initialdir="backups"
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                
                # تحديث الواجهة
                self.update_notebook()
                messagebox.showinfo("نجاح", "تم استعادة البيانات بنجاح")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استعادة البيانات: {str(e)}")
    
    def printer_settings(self):
        # فتح إعدادات الطابعة
        self.notebook.select(len(self.data) + 1)  # الانتقال إلى تبويب الإعدادات
    
    def customize_ui(self):
        # تخصيص الواجهة (محاكاة)
        messagebox.showinfo("تخصيص الواجهة", "هذه الميزة قيد التطوير. يمكنك تخصيص الألوان والخطوط في الإصدارات القادمة.")
    
    def show_help(self):
        # عرض دليل المستخدم
        help_text = """
        دليل استخدام برنامج المحاسبة المتكامل
        
        1. إدخال البيانات:
           - اختر القسم من التبويبات
           - أدخل التاريخ والقيم المطلوبة
           - انقر على زر "حفظ"
        
        2. إدارة البيانات:
           - انقر بزر الماوس الأيمن على أي سجل لعرض خيارات التعديل أو الحذف
           - استخدم قائمة "ملف" لفتح أو حفظ البيانات
        
        3. التقارير:
           - انقر على تبويب "التقرير الشامل" لعرض تقرير شامل لجميع الأقسام
           - استخدم الأزرار في كل قسم لإنشاء تقارير يومية أو شهرية
        
        4. الطباعة:
           - استخدم زر الطباعة في أي قسم لطباعة بياناته
           - من التقرير الشامل يمكنك طباعة التقرير الكامل
        
        5. الإعدادات:
           - من تبويب "الإعدادات" يمكنك ضبط إعدادات البرنامج
           - يمكنك تغيير الطابعة الافتراضية وتمكين/تعطيل الحفظ التلقائي
        
        للاستفسارات والدعم، يرجى التواصل مع المطور.
        """
        
        self.show_report_window(help_text, "دليل المستخدم")
    
    def about(self):
        # عرض معلومات عن البرنامج
        about_text = """
        برنامج المحاسبة المتكامل - الإصدار المحسن
        
        وصف البرنامج:
        برنامج محاسبي متكامل لإدارة الحسابات اليومية 
        لمختلف الأقسام والمشاريع التجارية الصغيرة.
        
        المميزات:
        - إدارة حسابات متعددة الأقسام
        - تقارير يومية وشهرية وسنوية
        - رسوم بيانية توضيحية
        - نسخ احتياطي واستعادة
        - واجهة مستخدم سهلة ومريحة
        
        الإصدار: 2.0
        تاريخ الإصدار: 2023
        المطور: فريق التطوير
        """
        
        self.show_report_window(about_text, "عن البرنامج")
    
    def confirm_exit(self):
        # تأكيد الخروج
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من الخروج؟"):
            if self.settings["backup_on_exit"]:
                self.backup_data()
            self.root.quit()

def main():
    try:
        root = tk.Tk()
        app = AccountingApp(root)
        root.mainloop()
    except Exception as e:
        messagebox.showerror("خطأ فادح", f"حدث خطأ غير متوقع: {str(e)}\n\n{''.join(traceback.format_exception(*sys.exc_info()))}")

if __name__ == "__main__":
    main()